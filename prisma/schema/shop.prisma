enum ShopStatus {
    PAUSED // 暂停营业
    ACTIVE // 可用
    CLOSED // 已关闭
}

model Shop {
    id             Int               @id @default(autoincrement()) @map("shop_id")
    name           String
    thumbnailImg   String?           @map("thumbnail_img")
    description    String?
    address        String?
    businessHours  String?           @map("business_hours")
    phone         String?
    status         ShopStatus        @default(ACTIVE) // 手动状态
    createdAt      DateTime          @default(now()) @map("created_at")
    updatedAt      DateTime?         @updatedAt @map("updated_at")
    latitude       Float?
    longitude      Float?
    stations       Station[]
    businessId     Int               @map("business_id")
    business       Business          @relation(fields: [businessId], references: [id])
    avgRating      Float             @default(0.0) // 评分
    carouselImages Carousel_images[]
    shopProduct    ShopProduct[]
    reviews        Review[]
    reviewCount    Int               @default(0)
    

    @@map("shops")
}

enum StationStatus {
    IN_USE // 已使用
    IDLE // 空闲
    MAINTENANCE // 维护中
}

model Station {
    id           Int           @id @default(autoincrement()) @map("station_id")
    name         String
    description  String?
    status       StationStatus @default(IDLE) // 手动状态
    createdAt    DateTime      @default(now()) @map("created_at")
    updatedAt    DateTime      @updatedAt @map("updated_at")
    shopId       Int           @map("shop_id")
    shop         Shop          @relation(fields: [shopId], references: [id])
    thumbnailImg String?       @map("thumbnail_img")

    price        Float
    consumeOrder ConsumeOrder[]

    @@map("stations")
}

model Carousel_images {
    id          Int      @id @default(autoincrement()) @map("carousel_id")
    shopId      Int      @map("shop_id")
    shop        Shop     @relation(fields: [shopId], references: [id])
    imageUrl    String   @map("image_url")
    order       Int
    title       String?
    description String?
    createdAt   DateTime @default(now()) @map("created_at")
    updatedAt   DateTime @updatedAt @map("updated_at")
    type        Int      @default(1) // 1 image, 2 video
    pageId      Int      @default(1) // 1 home, 2 shop

    @@map("carousel_images")
}

// 扩展 商店 商品表
model ShopProduct {
    id          Int      @id @default(autoincrement()) @map("shop_product_id")
    shopId      Int      @map("shop_id")
    shop        Shop     @relation(fields: [shopId], references: [id])
    name        String
    description String?
    price       Float
    createdAt   DateTime @default(now()) @map("created_at")
    updatedAt   DateTime @updatedAt @map("updated_at")

    @@map("shop_products")
}

// 评价
model Review {
    id         Int      @id @default(autoincrement()) @map("review_id")
    shopId     Int      @map("shop_id")
    shop       Shop     @relation(fields: [shopId], references: [id])
    shopUserId Int      @map("shop_user_id")
    shopUser   ShopUser @relation(fields: [shopUserId], references: [id])
    rating     Int // 1-5 星
    comment    String? // 用户评论
    images     Json? // 存储图片数组 ["url1","url2"]

    orderId String? @map("order_id") @db.VarChar(36) // 关联订单
    order   Order?  @relation(fields: [orderId], references: [id])

    createdAt DateTime @default(now()) @map("created_at")
    updatedAt DateTime @updatedAt @map("updated_at")
    isHidden  Boolean  @default(false) @map("is_hidden") // 管理员屏蔽（不显示）

    @@unique([shopUserId, orderId])
    @@index([shopId])
    @@map("reviews")
}
