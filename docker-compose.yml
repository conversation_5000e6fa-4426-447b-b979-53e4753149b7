# Docker Compose 配置文件
# version: "3.8"

services:
  # Next.js 应用
  app:
    build:
      context: .
      dockerfile: Dockerfile
      # target: runner
    container_name: ccxc-app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
      - DATABASE_URL=********************************/ccxc
      # - REDIS_URL=redis://redis:6379
      - JWT_SECRET=5a70e96661f773216a57f74be3154f164f3a25051a51bf5b1577a5edf7d3dc32d80d8837a8712b8e2f39093cddb65d3631bda06c8a98804af2bf4fe1e379b4e3
      # - NEXTAUTH_SECRET=your-nextauth-secret-change-in-production
      - PRISMA_ENGINES_MIRROR=https://registry.npmmirror.com/-/binary/prisma
      - NEXTAUTH_URL=http://localhost:3000
    depends_on:
      - db
      # - redis
    restart: unless-stopped
    networks:
      - app-network
    volumes:
      - uploads_data:/app/uploads
    # healthcheck:
    #   test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
    #   interval: 30s
    #   timeout: 10s
    #   retries: 3
    #   start_period: 40s

  # PostgreSQL 数据库
  db:
    image: postgres:15-alpine
    container_name: ccxc-db

    environment:
      POSTGRES_USER: root
      POSTGRES_PASSWORD: 123456
      POSTGRES_DB: ccxc
      TZ: Asia/Shanghai # 设置时区
    ports:
      - "5432:5432"
    volumes:
      - ./postgres_data:/var/lib/postgresql/data
      # - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    restart: unless-stopped
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: ccxc-redis
    ports:
      - "6379:6379"
    volumes:
      - ./redis_data:/data
    restart: unless-stopped
    environment:
      - REDIS_PASSWORD=123456
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: ccxc-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - uploads_data:/var/www/uploads
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - app-network

# 网络配置
networks:
  app-network:
    driver: bridge

# 数据卷
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  uploads_data:
    driver: local
