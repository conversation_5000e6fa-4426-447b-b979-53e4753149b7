# 运行时基础镜像，体积小
FROM node:20-slim AS runner

WORKDIR /app

# 设置运行环境
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV HOSTNAME=0.0.0.0

# 复制构建产物（从构建机输出目录）
COPY .next/standalone ./
COPY .next/static ./.next/static
COPY public ./public

<<<<<<< HEAD
# 上传目录（可挂载卷）
RUN mkdir -p /app/uploads
VOLUME ["/app/uploads"]
=======
# 安装依赖（使用缓存挂载）
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --frozen-lockfile

# RUN pnpm install --prod

# ========== 构建阶段 ==========
FROM node:22.13.0-slim AS builder
WORKDIR /app

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable && corepack prepare pnpm@9.12.3 --activate

# 复制依赖
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/pnpm-lock.yaml ./pnpm-lock.yaml
COPY --from=deps /app/package.json ./package.json

# 复制源码
COPY . .

# Prisma 生成（带缓存挂载）
RUN pnpm exec prisma generate

# 构建 Next.js
# ENV NODE_OPTIONS="--max-old-space-size=512"
# ENV CI=1
# ENV NEXT_TELEMETRY_DISABLED 1
# ENV NODE_ENV production
# RUN pnpm build --no-lint

#  RUN npx prisma generate
# RUN pnpm db:deploy

# ========== 运行阶段 ==========
FROM node:22.13.0-slim AS runner
WORKDIR /app

# 创建非 root 用户
# RUN addgroup --system --gid 1001 nodejs \
#     && adduser --system --uid 1001 nextjs
# RUN adduser --system --uid 1001 --home /home/<USER>
# ENV HOME=/home/<USER>

# 环境变量
ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

# 创建 uploads 文件夹
# RUN mkdir -p /app/uploads && chmod -R 777 /app/uploads

# 复制构建产物
COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

# 拷贝入口脚本
COPY docker-entrypoint.sh /app/docker-entrypoint.sh
RUN chmod +x /app/docker-entrypoint.sh

# 切换到非 root 用户
# USER nextjs
>>>>>>> 03cd8f1907de60e566d8f59e1334a9df8a85dc36

EXPOSE 3000

# 运行 Next.js
CMD ["node", "server.js"]
