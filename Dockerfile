
FROM node:22.13.0-slim   AS deps

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable && corepack prepare pnpm@9.12.3 --activate

COPY . /app
WORKDIR /app


# 复制依赖文件
COPY package.json pnpm-lock.yaml ./

# 安装依赖（使用缓存挂载）
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --frozen-lockfile

# RUN pnpm install --prod

# ========== 构建阶段 ==========
FROM node:22.13.0-slim AS builder
WORKDIR /app

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable && corepack prepare pnpm@9.12.3 --activate

# 复制依赖
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/pnpm-lock.yaml ./pnpm-lock.yaml
COPY --from=deps /app/package.json ./package.json

# 复制源码
COPY . .

# Prisma 生成（带缓存挂载）
RUN pnpm exec prisma generate

# 构建 Next.js
# ENV NODE_OPTIONS="--max-old-space-size=512"
# ENV CI=1
# ENV NEXT_TELEMETRY_DISABLED 1
# ENV NODE_ENV production
# RUN pnpm build --no-lint

#  RUN npx prisma generate
# RUN pnpm db:deploy

# ========== 运行阶段 ==========
FROM node:22.13.0-slim AS runner
WORKDIR /app

# 环境变量
ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

# 创建非 root 用户
# RUN addgroup --system --gid 1001 nodejs
# RUN adduser --system --uid 1001 nextjs
# RUN adduser --system --uid 1001 --home /home/<USER>
# ENV HOME=/home/<USER>




# 创建 uploads 文件夹
# RUN mkdir -p /app/uploads && chmod -R 777 /app/uploads

# 复制构建产物
COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/prisma ./prisma
COPY --from=builder  /app/.next/standalone ./
COPY --from=builder  /app/.next/static ./.next/static

# 拷贝入口脚本
COPY docker-entrypoint.sh /app/docker-entrypoint.sh
RUN chmod +x /app/docker-entrypoint.sh

# 切换到非 root 用户
# USER nextjs

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

EXPOSE 3000
ENTRYPOINT ["./docker-entrypoint.sh"]
CMD ["node", "server.js"]

