# 基础镜像：体积小
FROM node:22.13.0-slim AS runner

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable && corepack prepare pnpm@9.12.3 --activate

WORKDIR /app

# 环境变量
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV HOSTNAME=0.0.0.0

# 安装依赖
COPY package.json pnpm-lock.yaml ./
RUN   --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --prod --frozen-lockfile

# 复制 Prisma Schema
COPY prisma ./prisma

# 复制构建产物
COPY .next/standalone ./app 
COPY .next/static ./app/.next/static
COPY public ./app/public

# 拷贝入口脚本
COPY docker-entrypoint.sh /app/docker-entrypoint.sh
RUN chmod +x /app/docker-entrypoint.sh


# 上传目录
RUN mkdir -p /app/uploads
VOLUME ["/app/uploads"]

# 暴露端口
EXPOSE 3000

ENTRYPOINT ["./docker-entrypoint.sh"]
CMD ["node", "server.js"]
