/*
 * @Author: weizheng
 * @Date: 2021-06-22 20:30:54
 * @LastEditors: weizheng
 * @LastEditTime: 2021-07-17 17:42:17
 */
import React, {
  useState,
  useImperativeHandle,
  forwardRef,
  useMemo,
} from "react";
import { Table } from "antd";
import { BasicTableProps, TableRef } from "./types/table";
// import TableColumn from './compontent/TableColumn';
import TableAction from "./compontent/TableAction";
import { ColumnProps, TablePaginationConfig } from "antd/es/table";
import { SpinProps } from "antd/es/spin";
import { ColumnBasicProps } from "./types/table";

import { useDataSource } from "./hooks/useDataSource";
import { formatBeijingTime } from "@/utils/dateUtils";

const { Column, ColumnGroup } = Table;

const BasicTable: React.ForwardRefRenderFunction<TableRef, BasicTableProps> = (
  props,
  ref
) => {
  const {
    actions,
    columns,
    tableProps,
    actionWidth,
    actionLabel = "操作",
    actionProps,
  } = props;
  const [loading, setLoading] = useState<boolean | SpinProps | undefined>(
    props?.tableProps?.loading
  );
  const [pagination, setPagination] = useState<false | TablePaginationConfig>(
    tableProps?.pagination || false
  );

  const newColumns = useMemo(() => {
    return columns.map((item: ColumnBasicProps<Recordable>) => {
      if (item.type === "dateTime") {
        return {
          ...item,
          render: (text: string) => {
            return formatBeijingTime(text);
          },
        };
      }
      return {
        ...item,
        align: item.align ? item.align : "center",
      };
    });
  }, [columns]);

  const { handleTableChange, dataSource, getRowKey, fetch, reload } =
    useDataSource(props, {
      paginationInfo: pagination,
      setPagination,
      setLoading,
    });

  useImperativeHandle(ref, () => ({
    handleSearchFunc,
    reload,
  }));

  const handleSearchFunc = (params: Recordable) => {
    fetch({ searchInfo: params, page: 1 });
  };
  return (
    <Table
      {...tableProps}
      pagination={pagination}
      onChange={handleTableChange}
      loading={loading}
      dataSource={dataSource.data}
      rowKey={getRowKey()}
    >
      {newColumns?.map((item: ColumnProps<Recordable>, index: number) => {
        if (item.children) {
          const list: ColumnProps<Recordable>[] = item.children;
          return (
            <ColumnGroup title={item.title} key={index}>
              {list.map((child: ColumnProps<Recordable>, key) => (
                <Column
                  {...child}
                  align={child.align ? child.align : "center"}
                  key={key}
                />
              ))}
            </ColumnGroup>
          );
        } else {
          return (
            <Column
              {...item}
              align={item.align ? item.align : "center"}
              key={index}
            />
          );
        }
      })}
      <Column
        title={actionLabel}
        key="action"
        width={actionWidth}
        align={"center"}
        {...actionProps}
        render={(...params) => (
          <TableAction actions={actions} params={params} />
        )}
        // render={(...params) => (
        //   <Space size="middle">
        //     {actions.map((item: ActionItem) => (
        //       <a key={item.label} onClick={() => item.onClick && item.onClick(...params)}>
        //         {item.label}
        //       </a>
        //     ))}
        //   </Space>
        // )}
      />
    </Table>
  );
};
export default forwardRef(BasicTable);
