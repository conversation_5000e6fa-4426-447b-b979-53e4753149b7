import React, { useState, useEffect } from "react";
import { Upload, Image, message } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import type { UploadFile, UploadProps, GetProp } from "antd";
import ImgCrop from "antd-img-crop";
import type { ImgCropProps } from "antd-img-crop";

import { buildUUID } from "@/utils/uuid";
import { checkFileType, getBase64WithFile } from "./utils";
import { isString } from "@/utils/is";
import { defHttp } from "@/lib/axios";

type FileType = Parameters<GetProp<UploadProps, "beforeUpload">>[0];

const commonUploadApi = (params: any) => {
  return defHttp.uploadFile({ url: "/upload" }, params);
};
interface UploadParams {
  value?: any;
  onChange?: any;
  maxSize?: number;
  accept?: string[];
  maxCount?: number;
  multiple?: boolean;
  type?: string;
  category?: string;
  imgCrop?: ImgCropProps;
  limit: number;
}

const BasicUpload: React.FC<UploadParams & UploadProps> = (props) => {
  const { imgCrop: imgCropProps, ...UploadBasicProps } = props;

  const { limit = 1 } = UploadBasicProps;
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  useEffect(() => {
    if (props.value && isString(props.value)) {
      const valList: string[] = props.value.split(",");
      const list: UploadFile[] = [];
      valList.map((item) => {
        const nameArr = item.split("/");
        const nameStr = nameArr[nameArr.length - 1];
        const nameStrArr = nameStr.split("-");
        const nameStrStr = nameStrArr[nameStrArr.length - 1];
        const fileItem: UploadFile = {
          uid: buildUUID(),
          status: "done",
          url: item,
          name: nameStrStr,
        };
        list.push(fileItem);
      });
      setFileList(list);
    }
  }, [props.value]);

  const beforeUpload = (file: File) => {
    const { maxSize = 10, accept = [] } = props;
    if (maxSize && file.size / 1024 / 1024 >= maxSize) {
      message.error(`文件不能超出${maxSize}MB`);
      return false;
    }
    // 设置类型,则判断
    if (accept.length > 0 && !checkFileType(file, accept)) {
      message.error(`文件不能超出${maxSize}MB`);
      return false;
    }
    return false;
  };

  const handleChange = async (info: any) => {
    const { maxCount = Infinity, onChange } = props;

    const list = info.fileList;
    const valList: string[] = [];
    if (list.length > 0 && list.length > maxCount) {
      message.error(`文件最多上传${maxCount}个`);
      return false;
    }
    for (let index = 0; index < list.length; index++) {
      list[index].status = "uploading";
      if (list[index].status !== "done") {
        const { data } = await commonUploadApi({
          file: list[index].originFileObj,
          type: props.type,
          category: props.category,
        });
        console.log("upload", data.data);
        const obj = data.data;
        list[index].url = obj.url;
        valList.push(obj.url);
        list[index].percent = 100;
        list[index].name = obj.name;
        list[index].status = "done";
      } else {
        valList.push(list[index].url);
      }
    }
    setFileList(list);
    console.log(valList);
    onChange(valList);
  };
  const { multiple = true, accept = [] } = props;
  const getStringAccept = () => {
    return accept.map((item) => `.${item}`).join(",");
  };
  const onRemove = (file: UploadFile) => {
    const index = fileList.findIndex((item) => item.uid === file.uid);
    if (index !== -1) {
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    }
  };
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState("");
  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      getBase64WithFile(file.originFileObj as FileType).then((res) => {
        file.preview = res.result;
      });
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
  };

  const uploadButton = (
    <button style={{ border: 0, background: "none" }} type="button">
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </button>
  );

  return (
    <>
      <ImgCrop rotationSlider {...imgCropProps}>
        <Upload
          {...UploadBasicProps}
          fileList={fileList}
          beforeUpload={beforeUpload}
          onChange={handleChange}
          multiple={multiple}
          accept={getStringAccept()}
          onRemove={onRemove}
          onPreview={handlePreview}
        >
          {fileList.length < limit && uploadButton}
        </Upload>
      </ImgCrop>
      {previewImage && (
        <Image
          wrapperStyle={{ display: "none" }}
          preview={{
            visible: previewOpen,
            onVisibleChange: (visible) => setPreviewOpen(visible),
            afterOpenChange: (visible) => !visible && setPreviewImage(""),
          }}
          src={previewImage}
        />
      )}
    </>
  );
};

export default BasicUpload;
