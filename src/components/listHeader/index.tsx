import React from "react";
import { Flex, Space, Button, Typography } from "antd";
import {
  ReloadOutlined,
  ExportOutlined,
  PlusOutlined,
} from "@ant-design/icons";

const { Title } = Typography;

interface ListHeaderProps {
  title: string;
  description?: string;
  onRefresh: () => void;
  onExport: () => void;
  onCreate: () => void;
}

const ListHeader: React.FC<ListHeaderProps> = ({
  title,
  description,
  onRefresh,
  onExport,
  onCreate,
}) => {
  return (
    <Flex justify="space-between" align="center">
      <Title level={3}>{title}</Title>
      <Space>
        <Button icon={<ReloadOutlined />} onClick={onRefresh}>
          刷新
        </Button>
        <Button icon={<ExportOutlined />} onClick={onExport}>
          导出
        </Button>
        <Button type="primary" icon={<PlusOutlined />} onClick={onCreate}>
          新增店铺
        </Button>
      </Space>
    </Flex>
  );
};

export default ListHeader;
