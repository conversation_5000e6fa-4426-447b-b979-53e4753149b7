"use client";

import React, { useState, useEffect } from "react";
import {
  Space,
  Tag,
  Badge,
  Modal,
  Form,
  Tree,
  Tabs,
  Descriptions,
  message,
  Tooltip,
  Button,
} from "antd";
import {
  SafetyOutlined,
  UserOutlined,
  SettingOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  KeyOutlined,
} from "@ant-design/icons";
import AuthGuard from "@/components/auth/AuthGuard";
import { DataTable, commonRowActions, commonFilters } from "@/components/table";
import {
  BaseForm,
  TextField,
  TextAreaField,
  SelectField,
  SwitchField,
} from "@/components/Form";
import { useUserStore } from "@/store/useUserStore";
// import { http } from "@/lib/request";
import type { ColumnsType } from "antd/es/table";

const { TabPane } = Tabs;

// 角色数据类型
interface Role {
  id: number;
  name: string;
  code: string;
  description: string;
  permissions: string[];
  status: "active" | "inactive";
  isSystem: boolean;
  userCount: number;
  createdAt: string;
  updatedAt: string;
}

// 权限数据类型
interface Permission {
  id: string;
  name: string;
  code: string;
  category: string;
  description: string;
  type: "menu" | "button" | "api";
  parentId?: string;
  children?: Permission[];
}

// 分页信息类型
interface PaginationInfo {
  current: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

export default function RolesPage() {
  const { userInfo } = useUserStore();
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<PaginationInfo>({
    current: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  });

  // 模态框状态
  const [modalVisible, setModalVisible] = useState(false);
  const [modalType, setModalType] = useState<
    "create" | "edit" | "view" | "permissions"
  >("create");
  const [currentRole, setCurrentRole] = useState<Role | null>(null);
  const [form] = Form.useForm();

  // 权限分配状态
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);

  // 状态选项
  const statusOptions = [
    { label: "启用", value: "active", color: "success" },
    { label: "禁用", value: "inactive", color: "warning" },
  ];

  // 获取角色列表
  const fetchRoles = async (page = 1, pageSize = 10, filters = {}) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
        ...filters,
      });

      // const response = await http.get(`/roles?${params}`);

      // if (response.data.success) {
      //   setRoles(response.data.data.list);
      //   setPagination(response.data.data.pagination);
      // } else {
      //   message.error(response.data.message || "获取角色列表失败");
      // }
    } catch (error) {
      console.error("获取角色列表错误:", error);
      message.error("获取角色列表失败");
    } finally {
      setLoading(false);
    }
  };

  // 获取权限列表
  const fetchPermissions = async () => {
    // try {
    //   const response = await http.get("/permissions?tree=true");

    //   if (response.data.success) {
    //     setPermissions(response.data.data);
    //   } else {
    //     message.error(response.data.message || "获取权限列表失败");
    //   }
    // } catch (error) {
    //   console.error("获取权限列表错误:", error);
    //   message.error("获取权限列表失败");
    // }
  };

  // 初始化加载
  useEffect(() => {
    fetchRoles();
    fetchPermissions();
  }, []);

  // 表格列定义
  const columns: ColumnsType<Role> = [
    {
      title: "角色信息",
      key: "roleInfo",
      width: 200,
      render: (_, record) => (
        <Space>
          <SafetyOutlined
            style={{
              fontSize: "24px",
              color: record.isSystem ? "#ff4d4f" : "#1890ff",
            }}
          />
          <div>
            <div
              style={{
                fontWeight: "bold",
                display: "flex",
                alignItems: "center",
                gap: "8px",
              }}
            >
              {record.name}
            </div>
            <div style={{ fontSize: "12px", color: "#666" }}>{record.code}</div>
          </div>
        </Space>
      ),
    },
    {
      title: "描述",
      dataIndex: "description",
      key: "description",
      width: 200,
      ellipsis: true,
    },
    {
      title: "权限数量",
      key: "permissionCount",
      width: 100,
      render: (_, record) => (
        <Tag color="blue">{record.permissions.length}</Tag>
      ),
    },
    {
      title: "用户数量",
      key: "userCount",
      width: 100,
      render: (_, record) => (
        <Space>
          <UserOutlined />
          <span>{record.userCount}</span>
        </Space>
      ),
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status: string) => {
        const statusOption = statusOptions.find((opt) => opt.value === status);
        return (
          <Badge
            status={statusOption?.color as any}
            text={statusOption?.label || status}
          />
        );
      },
    },
    {
      title: "创建时间",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 150,
      render: (time: string) => new Date(time).toLocaleString(),
    },
  ];

  // 打开模态框
  const openModal = (type: typeof modalType, role?: Role) => {
    setModalType(type);
    setCurrentRole(role || null);
    setModalVisible(true);

    if (type === "edit" && role) {
      form.setFieldsValue({
        name: role.name,
        code: role.code,
        description: role.description,
        status: role.status,
      });
    } else if (type === "permissions" && role) {
      setSelectedPermissions(role.permissions);
      setExpandedKeys(permissions.map((p) => p.id));
    } else if (type === "create") {
      form.resetFields();
    }
  };

  // 关闭模态框
  const closeModal = () => {
    setModalVisible(false);
    setCurrentRole(null);
    setSelectedPermissions([]);
    form.resetFields();
  };

  // 处理角色提交
  const handleRoleSubmit = async (values: any) => {
    try {
      setLoading(true);

      // if (modalType === "edit" && currentRole) {
      //   const response = await http.put(`/roles/${currentRole.id}`, values);
      //   if (response.data.success) {
      //     message.success("更新角色成功");
      //     closeModal();
      //     fetchRoles(pagination.current, pagination.pageSize);
      //   } else {
      //     message.error(response.data.message || "更新角色失败");
      //   }
      // } else if (modalType === "create") {
      //   const response = await http.post("/roles", values);
      //   if (response.data.success) {
      //     message.success("创建角色成功");
      //     closeModal();
      //     fetchRoles(pagination.current, pagination.pageSize);
      //   } else {
      //     message.error(response.data.message || "创建角色失败");
      //   }
      // }
    } catch (error) {
      console.error("角色操作错误:", error);
      message.error("操作失败");
    } finally {
      setLoading(false);
    }
  };

  // 处理权限分配
  const handlePermissionAssign = async () => {
    if (!currentRole) return;

    try {
      setLoading(true);
      // const response = await http.put(`/roles/${currentRole.id}`, {
      //   permissions: selectedPermissions,
      // });

      // if (response.data.success) {
      //   message.success("权限分配成功");
      //   closeModal();
      //   fetchRoles(pagination.current, pagination.pageSize);
      // } else {
      //   message.error(response.data.message || "权限分配失败");
      // }
    } catch (error) {
      console.error("权限分配错误:", error);
      message.error("权限分配失败");
    } finally {
      setLoading(false);
    }
  };

  // 删除角色
  const handleDelete = async (role: Role) => {
    try {
      setLoading(true);
      // const response = await http.delete(`/roles/${role.id}`);

      // if (response.data.success) {
      //   message.success("删除角色成功");
      //   fetchRoles(pagination.current, pagination.pageSize);
      // } else {
      //   message.error(response.data.message || "删除角色失败");
      // }
    } catch (error) {
      console.error("删除角色错误:", error);
      message.error("删除角色失败");
    } finally {
      setLoading(false);
    }
  };

  // 构建权限树数据
  const buildTreeData = (permissions: Permission[]): any[] => {
    return permissions.map((permission) => ({
      title: (
        <Space>
          <span>{permission.name}</span>
          <Tag
            color={
              permission.type === "menu"
                ? "blue"
                : permission.type === "button"
                ? "green"
                : "orange"
            }
          >
            {permission.type === "menu"
              ? "菜单"
              : permission.type === "button"
              ? "按钮"
              : "API"}
          </Tag>
        </Space>
      ),
      key: permission.code,
      children: permission.children
        ? buildTreeData(permission.children)
        : undefined,
    }));
  };

  return (
    <AuthGuard requiredPermissions={["roles:read"]}>
      <div style={{ padding: "0" }}>
        <DataTable<Role>
          title="角色管理"
          description="管理系统角色和权限分配"
          dataSource={roles}
          columns={columns}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
          }}
          searchable={true}
          searchPlaceholder="搜索角色名称、代码或描述"
          filters={[commonFilters.status(statusOptions)]}
          actions={{
            refresh: {
              onClick: () =>
                fetchRoles(pagination.current, pagination.pageSize),
            },
            create: {
              text: "新增角色",
              onClick: () => openModal("create"),
            },
          }}
          rowActions={[
            {
              key: "view",
              icon: <EyeOutlined />,
              tooltip: "查看详情",
              onClick: (record) => openModal("view", record),
            },
            {
              key: "permissions",
              icon: <KeyOutlined />,
              tooltip: "分配权限",
              onClick: (record) => openModal("permissions", record),
              visible: (record) => !record.isSystem,
            },
            commonRowActions.edit((record) => openModal("edit", record)),
            commonRowActions.delete(handleDelete, {
              visible: (record) => !record.isSystem && record.userCount === 0,
            }),
          ]}
          onSearch={(keyword) =>
            fetchRoles(1, pagination.pageSize, { keyword })
          }
          onFilter={(key, value) =>
            fetchRoles(1, pagination.pageSize, { [key]: value })
          }
          scroll={{ x: 1200 }}
        />

        {/* 角色表单模态框 */}
        <Modal
          title={
            modalType === "create"
              ? "新增角色"
              : modalType === "edit"
              ? "编辑角色"
              : modalType === "view"
              ? "角色详情"
              : "分配权限"
          }
          open={modalVisible}
          onCancel={closeModal}
          footer={null}
          width={modalType === "permissions" ? 800 : 600}
          destroyOnClose
        >
          {modalType === "view" && currentRole ? (
            <Descriptions column={1} bordered>
              <Descriptions.Item label="角色名称">
                {currentRole.name}
              </Descriptions.Item>
              <Descriptions.Item label="角色代码">
                {currentRole.code}
              </Descriptions.Item>
              <Descriptions.Item label="角色描述">
                {currentRole.description}
              </Descriptions.Item>
              <Descriptions.Item label="权限数量">
                <Tag color="blue">{currentRole.permissions.length}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="用户数量">
                <Space>
                  <UserOutlined />
                  <span>{currentRole.userCount}</span>
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Badge
                  status={
                    currentRole.status === "active" ? "success" : "warning"
                  }
                  text={currentRole.status === "active" ? "启用" : "禁用"}
                />
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {new Date(currentRole.createdAt).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {new Date(currentRole.updatedAt).toLocaleString()}
              </Descriptions.Item>
            </Descriptions>
          ) : modalType === "permissions" && currentRole ? (
            <div>
              <div style={{ marginBottom: "16px" }}>
                <h4>为角色 &quot;{currentRole.name}&quot; 分配权限</h4>
                <p style={{ color: "#666" }}>请选择该角色应该拥有的权限</p>
              </div>
              <Tree
                checkable
                checkedKeys={selectedPermissions}
                expandedKeys={expandedKeys}
                onCheck={(checkedKeys) => {
                  setSelectedPermissions(checkedKeys as string[]);
                }}
                onExpand={(expandedKeys) => {
                  setExpandedKeys(expandedKeys as string[]);
                }}
                treeData={buildTreeData(permissions)}
                height={400}
              />
              <div
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  gap: "8px",
                  marginTop: "16px",
                  paddingTop: "16px",
                  borderTop: "1px solid #f0f0f0",
                }}
              >
                <Button onClick={closeModal}>取消</Button>
                <Button
                  type="primary"
                  loading={loading}
                  onClick={handlePermissionAssign}
                >
                  保存权限
                </Button>
              </div>
            </div>
          ) : (
            <BaseForm
              form={form}
              loading={loading}
              onFinish={handleRoleSubmit}
              onCancel={closeModal}
              submitText={modalType === "create" ? "创建" : "更新"}
            >
              <TextField
                label="角色名称"
                name="name"
                required
                placeholder="请输入角色名称"
              />

              <TextField
                label="角色代码"
                name="code"
                required
                placeholder="请输入角色代码"
                disabled={modalType === "edit"}
                help="角色代码用于系统内部识别，创建后不可修改"
              />

              <TextAreaField
                label="角色描述"
                name="description"
                placeholder="请输入角色描述"
                rows={3}
              />

              <SelectField
                label="状态"
                name="status"
                required
                placeholder="请选择状态"
                options={statusOptions}
              />
            </BaseForm>
          )}
        </Modal>
      </div>
    </AuthGuard>
  );
}
