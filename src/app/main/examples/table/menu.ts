/*
 * @Author: weizheng
 * @Date: 2021-07-17 16:54:03
 * @LastEditors: weizheng
 * @LastEditTime: 2021-07-17 19:05:38
 */

import { defHttp } from '@/lib/axios';
import { ColumnBasicProps } from '@/components/table';
export const columns: ColumnBasicProps<Recordable>[] = [
  {
    title: '菜单名称',
    dataIndex: 'name',
    width: 200,
    align: 'left',
  },
  {
    title: '图标',
    dataIndex: 'iphone',
    width: 100,
  },
  {
    title: '权限标识',
    dataIndex: 'businessHours',
    width: 300,
  },
  {
    title: '组件',
    dataIndex: 'address',
  },
  {
    title: '排序',
    dataIndex: 'reviewCount',
    width: 50,
  },
];

export const menuListApi = (params: any) => {
  return defHttp.get({ url: '/shop', params });
};
