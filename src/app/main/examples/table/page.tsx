"use client";

import React, { useState, useRef } from "react";
import { Space, Avatar, Tag, Badge, message } from "antd";
import {
  UserOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import { columns, menuListApi } from "./menu";

import { BasicTable, TableRef } from "@/components/Table";

// 示例数据类型
interface ExampleUser {
  id: number;
  username: string;
  nickname: string;
  email: string;
  phone?: string;
  avatar?: string;
  status: "active" | "inactive" | "banned";
  roles: string[];
  createdAt: string;
  updatedAt: string;
  lastLoginTime?: string;
}

interface State {
  visible: boolean;
  tableItem: Nullable<ExampleUser>;
}

export default function TableExamplePage() {
  const tableRef = useRef<NonNullable<TableRef>>(null);
  const [state, setState] = useState<State>({
    visible: false,
    tableItem: null,
  });
  const handleClick = () => {
    message.success("删除成功");
  };
  const handleEdit = (record: ExampleUser) => {
    setState({ visible: true, tableItem: record });
  };

  return (
    <div style={{ padding: "0" }}>
      <BasicTable
        ref={tableRef}
        api={menuListApi}
        tableProps={{ pagination: false }}
        columns={columns}
        rowKey={"menuId"}
        actionProps={{ title: "操作", width: 200, fixed: "right" }}
        actions={[
          {
            label: "",
            icon: EditOutlined,
            onClick: handleEdit,
            ifShow: (record) => record.orderNum !== 1,
          },
          {
            label: "",
            icon: DeleteOutlined,
            color: "error",
            popConfirm: { title: "是否删除？", confirm: handleClick },
          },
        ]}
      />
    </div>
  );
}
