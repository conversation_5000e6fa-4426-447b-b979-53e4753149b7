"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Avatar,
  Modal,
  Form,
  Input,
  Select,
  Typography,
  Popconfirm,
  message,
  Badge,
  Tooltip,
  Upload,
  DatePicker,
} from "antd";
import ImgCrop from "antd-img-crop";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  ExportOutlined,
  EyeOutlined,
  UserOutlined,
} from "@ant-design/icons";
import type { ColumnsType, TablePaginationConfig } from "antd/es/table";
import { useUserStore } from "@/store/useUserStore";
import { defHttp } from "@/lib/axios";
import { useRouter } from "next/navigation";
import { getImageUrl } from "@/utils/image";

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

// 商户数据接口
interface ShopUser {
  id: number;
  username: string;
  nickname: string;
  phone: string;
  email: string;
  status: number;
  createdAt: string;
  updatedAt: string;
  balance: number;
  points: number;
  avatar: string;
  birthday?: string;
  gender: number;
}

// 分页信息接口
interface PaginationInfo {
  current: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

// 表单数据接口
interface ShopUserFormData {
  username: string;
  nickname: string;
  phone: string;
  email: string;
  status: number;
  balance: number;
  points: number;
  avatar: string;
  birthday?: string;
  gender: number;
}

const UsersPage: React.FC = () => {
  const router = useRouter();
  const [shopUsers, setShopUsers] = useState<ShopUser[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<ShopUser | null>(null);
  const [form] = Form.useForm<ShopUserFormData>();
  const [pagination, setPagination] = useState<PaginationInfo>({
    current: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  });
  const [searchKeyword, setSearchKeyword] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("");
  const { userInfo } = useUserStore();
  const [imageUrl, setImageUrl] = useState<string | null>(null);

  // 获取商户用户列表
  const fetchShopUsers = async (page = 1, pageSize = 10) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
        ...(searchKeyword && { keyword: searchKeyword }),
        ...(statusFilter && { status: statusFilter }),
      });

      const response = await defHttp.get({ url: `/shopUser`, params });

      setShopUsers(response.list);
      setPagination(response.pagination);
    } catch (error) {
      console.error("获取商户用户列表错误:", error);
      message.error("获取商户用户列表失败");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchShopUsers();
  }, []);

  // 搜索商户用户
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 状态筛选
  const handleStatusFilter = (value: string) => {
    setStatusFilter(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 分页变化
  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    fetchShopUsers(current, pageSize);
  };

  // 新增/编辑商户用户
  const handleSave = async (values: ShopUserFormData) => {
    try {
      setLoading(true);

      if (editingUser) {
        // 编辑
        await defHttp.put({
          url: `/shopUser/${editingUser.id}`,
          data: values,
        });
        message.success("更新商户用户成功");
        setModalVisible(false);
        setEditingUser(null);
        form.resetFields();
        fetchShopUsers(pagination.current, pagination.pageSize);
      } else {
        // 新增
        await defHttp.post({ url: "/shopUser", data: values });

        message.success("创建商户用户成功");
        setModalVisible(false);
        setEditingUser(null);
        form.resetFields();
        fetchShopUsers(pagination.current, pagination.pageSize);
      }
    } catch (error) {
      console.error("提交表单错误:", error);
      message.error("操作失败");
    } finally {
      setLoading(false);
    }
  };

  // 删除商户用户
  const handleDelete = async (userId: number) => {
    try {
      setLoading(true);
      const response = await defHttp.delete({ url: `/shopUser/${userId}` });

      if (response.success) {
        message.success("删除商户用户成功");
        fetchShopUsers(pagination.current, pagination.pageSize);
      } else {
        message.error(response.message || "删除商户用户失败");
      }
    } catch (error) {
      console.error("删除商户用户错误:", error);
      message.error("删除商户用户失败");
    } finally {
      setLoading(false);
    }
  };

  const beforeUpload = (file: File) => {
    const isJpgOrPng = file.type === "image/jpeg" || file.type === "image/png";
    if (!isJpgOrPng) {
      message.error("只能上传JPG/PNG文件!");
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error("图片大小不能超过2MB!");
    }
    return isJpgOrPng && isLt2M;
  };
  const handleAvatarUpload = (info: any) => {
    if (info.file.status === "done") {
      const avatarUrl = info.file.response?.data.url;
      if (avatarUrl) {
        setImageUrl(avatarUrl);
        form.setFieldsValue({ avatar: avatarUrl });
        message.success("头像上传成功");
      }
    } else if (info.file.status === "error") {
      message.error("头像上传失败");
    }
  };
  const uploadButton = (
    <button style={{ border: 0, background: "none" }} type="button">
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </button>
  );

  const statusOptions = [
    { label: "正常", value: "1", color: "success" },
    { label: "禁用", value: "0", color: "warning" },
  ];
  // 表格列配置
  const columns: ColumnsType<ShopUser> = [
    {
      title: "用户信息",
      key: "userInfo",
      width: 200,
      render: (_, record) => (
        <Space>
          <Avatar
            size={40}
            icon={<UserOutlined />}
            src={getImageUrl(record.avatar)}
          />
          <div>
            <div style={{ fontWeight: "bold" }}>{record.nickname}</div>
            <div style={{ fontSize: "12px", color: "#666" }}>
              @{record.username}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: "联系方式",
      key: "contact",
      width: 180,
      render: (_, record) => (
        <div>
          <div>{record.email}</div>
          {record.phone && (
            <div style={{ fontSize: "12px", color: "#666" }}>
              {record.phone}
            </div>
          )}
        </div>
      ),
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status: number) => (
        <Badge
          status={status === 1 ? "success" : "error"}
          text={status === 1 ? "正常" : "禁用"}
        />
      ),
    },
    {
      title: "操作",
      key: "actions",
      width: 120,
      fixed: "right",
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => {
                setEditingUser(record);
                form.setFieldsValue({
                  username: record.username,
                  nickname: record.nickname,
                  phone: record.phone,
                  email: record.email,
                  status: record.status,
                  balance: record.balance,
                  points: record.points,
                  avatar: record.avatar,
                  birthday: record.birthday,
                  gender: record.gender,
                });
                setModalVisible(true);
              }}
            />
          </Tooltip>
          {record.id && (
            <Popconfirm
              title="确定要删除这个商户用户吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip title="删除">
                <Button
                  type="text"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Tooltip>
            </Popconfirm>
          )}
          <Tooltip title="查看">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => {
                router.push(`/main/shopUser/detail?id=${record.id}`);
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: "0" }}>
      <Card>
        {/* 页面标题和操作按钮 */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "16px",
          }}
        >
          <div>
            <h2 style={{ margin: 0 }}>商户用户管理</h2>
            <p style={{ margin: "4px 0 0 0", color: "#666" }}>
              管理系统商户用户，包括商户用户信息、角色权限等{shopUsers.length}
            </p>
          </div>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() =>
                fetchShopUsers(pagination.current, pagination.pageSize)
              }
            >
              刷新
            </Button>
            <Button
              icon={<ExportOutlined />}
              onClick={() => message.info("导出功能开发中")}
            >
              导出
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingUser(null);
                form.resetFields();
                setModalVisible(true);
              }}
            >
              新增商户用户
            </Button>
          </Space>
        </div>

        {/* 搜索和筛选 */}
        <div style={{ marginBottom: "16px" }}>
          <Search
            placeholder="搜索商户用户名或昵称"
            onSearch={handleSearch}
            style={{ width: 200 }}
          />
        </div>

        {/* 商户用户表格 */}
        <Table
          columns={columns}
          dataSource={shopUsers}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 新增/编辑商户用户弹窗 */}
      <Modal
        title={editingUser ? "编辑商户用户" : "新增商户用户"}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingUser(null);
          form.resetFields();
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          initialValues={{
            status: "1",
          }}
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[
              { required: true, message: "请输入用户名" },
              { min: 3, message: "用户名至少3个字符" },
              {
                pattern: /^[a-zA-Z0-9_]+$/,
                message: "用户名只能包含字母、数字和下划线",
              },
            ]}
          >
            <Input placeholder="请输入用户名" />
            <p style={{ fontSize: "12px", color: "#666" }}>
              用户名用于登录，不能重复
            </p>
          </Form.Item>

          <Form.Item
            name="nickname"
            label="昵称"
            rules={[{ required: true, message: "请输入昵称" }]}
          >
            <Input placeholder="请输入昵称" />
          </Form.Item>

          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { required: true, message: "请输入邮箱" },
              { type: "email", message: "请输入有效的邮箱地址" },
            ]}
          >
            <Input placeholder="请输入邮箱地址" />
          </Form.Item>

          <Form.Item
            name="phone"
            label="手机号"
            rules={[
              { pattern: /^1[3-9]\d{9}$/, message: "请输入有效的手机号码" },
            ]}
          >
            <Input placeholder="请输入手机号码" />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: "请选择状态" }]}
          >
            <Select placeholder="请选择状态">
              {statusOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="balance"
            label="余额"
            rules={[{ required: true, message: "请输入余额" }]}
          >
            <Input placeholder="请输入余额" />
          </Form.Item>
          <Form.Item
            name="points"
            label="积分"
            rules={[{ required: true, message: "请输入积分" }]}
          >
            <Input placeholder="请输入积分" />
          </Form.Item>
          <Form.Item
            name="avatar"
            label="头像"
            rules={[{ required: true, message: "请输入头像" }]}
          >
            <ImgCrop rotationSlider>
              <Upload
                name="file"
                listType="picture-card"
                className="avatar-uploader"
                action="/api/upload"
                onChange={handleAvatarUpload}
                beforeUpload={beforeUpload}
                showUploadList={false}
              >
                {imageUrl ? (
                  <img src={imageUrl} alt="avatar" style={{ width: "100%" }} />
                ) : (
                  uploadButton
                )}
              </Upload>
            </ImgCrop>
          </Form.Item>
          <Form.Item
            name="birthday"
            label="生日"
            rules={[{ required: true, message: "请输入生日" }]}
          >
            <DatePicker />
          </Form.Item>
          <Form.Item
            name="gender"
            label="性别"
            rules={[{ required: true, message: "请输入性别" }]}
          >
            <Input placeholder="请输入性别" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: "right" }}>
            <Space>
              <Button
                onClick={() => {
                  setModalVisible(false);
                  setEditingUser(null);
                  form.resetFields();
                }}
              >
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingUser ? "更新" : "创建"}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UsersPage;
