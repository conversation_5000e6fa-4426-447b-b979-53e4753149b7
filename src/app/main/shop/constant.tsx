import { ColumnsType } from "antd/es/table";
import { Space, Avatar, Badge } from "antd";
import { UserOutlined } from "@ant-design/icons";
import { FormSchema } from "@/components/Form";

export const statusOptions = [
  { label: "正常", value: "ACTIVE", color: "success" },
  { label: "暂停营业", value: "PAUSED", color: "warning" },
  { label: "已关闭", value: "CLOSED", color: "error" },
];

export interface Shop {
  id: number;
  name: string;
  description: string;
  address: string;
  iphone: string;
  status: string;
  businessHours: string;
  createdAt: string;
  updatedAt: string;
  shopId: number;
  thumbnailImg?: string;
  businessId: number;
  carouselImages: string[];
  avgRating: number;
}

export const columns: ColumnsType<Shop> = [
  {
    title: "店铺信息",
    key: "name",
    width: 200,
    render: (_, record) => (
      <Space>
        <Avatar
          size={40}
          shape="square"
          icon={<UserOutlined />}
          src={record.thumbnailImg}
        />

        <div>
          <div style={{ fontWeight: "bold" }}>{record.name}</div>
          <div style={{ fontSize: "12px", color: "#666" }}>
            @{record.description}
          </div>
        </div>
      </Space>
    ),
  },
  {
    title: "联系方式",
    key: "contact",
    width: 180,
    render: (_, record) => (
      <div>
        <div>{record.iphone}</div>
        {record.iphone && (
          <div style={{ fontSize: "12px", color: "#666" }}>{record.iphone}</div>
        )}
      </div>
    ),
  },
  {
    title: "店铺时间",
    dataIndex: "businessHours",
    key: "businessHours",
    width: 150,
  },
  {
    title: "状态",
    dataIndex: "status",
    key: "status",
    width: 100,
    render: (status: string) => {
      const statusOption = statusOptions.find((opt) => opt.value === status);
      return (
        <Badge
          status={statusOption?.color as any}
          text={statusOption?.label || status}
        />
      );
    },
  },
  {
    title: "创建时间",
    dataIndex: "createdAt",
    key: "createdAt",
    width: 150,
    render: (time: string) => new Date(time).toLocaleString(),
  },
];

export const searchScheams: FormSchema[] = [
  {
    field: "name",
    label: "店铺名称",
    component: "InputSearch",
    componentprops: {
      placeholder: "请输入店铺名称",
    },
    optionApi: () =>
      Promise.resolve([
        { label: "店铺1", value: "店铺1" },
        { label: "店铺2", value: "店铺2" },
      ]),
  },
  {
    field: "status",
    label: "状态",
    component: "Select",
    componentprops: {
      options: statusOptions,
    },
  },
];
