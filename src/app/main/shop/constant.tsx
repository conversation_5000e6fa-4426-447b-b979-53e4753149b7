import { Space, Avatar, Badge, Image } from "antd";
import { UserOutlined } from "@ant-design/icons";
import { FormSchema, FormActionType } from "@/components/form";
import { defHttp } from "@/lib/axios";
import debounce from "lodash-es/debounce";
import { ColumnBasicProps } from "@/components/table";
import { getImageUrl } from "@/utils/image";

export const statusOptions = [
  { label: "正常", value: "ACTIVE", color: "success" },
  { label: "暂停营业", value: "PAUSED", color: "warning" },
  { label: "已关闭", value: "CLOSED", color: "error" },
];

export interface Shop {
  id: number;
  name: string;
  description: string;
  address: string;
  phone: string;
  status: string;
  businessHours: string;
  createdAt: string;
  updatedAt: string;
  shopId: number;
  thumbnailImg?: string;
  businessId: number;
  carouselImages: string[];
  avgRating: number;
}

export const columns: ColumnBasicProps<Shop>[] = [
  {
    title: "店铺信息",
    key: "name",
    width: 200,
    render: (_, record) => (
      <Space>
        <Avatar
          size={40}
          shape="square"
          icon={<UserOutlined />}
          src={getImageUrl(record.thumbnailImg)}
        />

        <div>
          <div style={{ fontWeight: "bold" }}>{record.name}</div>
          <div style={{ fontSize: "12px", color: "#666" }}>
            @{record.description}
          </div>
        </div>
      </Space>
    ),
  },
  {
    title: "联系方式",
    key: "contact",
    width: 180,
    render: (_, record) => (
      <div>
        <div>{record.phone}</div>
        {record.phone && (
          <div style={{ fontSize: "12px", color: "#666" }}>{record.phone}</div>
        )}
      </div>
    ),
  },
  {
    title: "商户",
    dataIndex: "business",
    key: "business",
    width: 150,
    render: (business: any) => business?.name,
  },
  {
    title: "店铺图片",
    dataIndex: "thumbnailImg",
    key: "thumbnailImg",
    width: 150,
    render: (thumbnailImg: string) => (
      <Image src={getImageUrl(thumbnailImg)} width={80} />
    ),
  },
  {
    title: "店铺时间",
    dataIndex: "businessHours",
    key: "businessHours",
    width: 150,
  },
  {
    title: "评分",
    dataIndex: "avgRating",
    key: "avgRating",
    width: 100,
  },
  {
    title: "地址",
    dataIndex: "address",
    key: "address",
    width: 150,
  },
  {
    title: "工位数量",
    dataIndex: "stations",
    key: "stations",
    width: 100,
    render: (stations: any) => stations.length,
  },
  {
    title: "状态",
    dataIndex: "status",
    key: "status",
    width: 100,
    render: (status: string) => {
      const statusOption = statusOptions.find((opt) => opt.value === status);
      return (
        <Badge
          status={statusOption?.color as any}
          text={statusOption?.label || status}
        />
      );
    },
  },
  {
    title: "创建时间",
    dataIndex: "createdAt",
    key: "createdAt",
    width: 150,
    type: "dateTime",
  },
];

export const searchScheams: FormSchema[] = [
  {
    field: "name",
    label: "店铺名称",
    component: "Input",
    componentprops: {
      placeholder: "请输入店铺名称",
    },
  },
  {
    field: "status",
    label: "状态",
    component: "Select",
    componentprops: {
      options: statusOptions,
    },
  },
];

const fetchBusiness = async (value: string) => {
  return defHttp.get({
    url: `/business`,
    params: { name: value, pageSize: 100 },
  });
};

export const addScheams: FormSchema[] = [
  {
    field: "name",
    label: "店铺名称",
    component: "Input",
    componentprops: {
      placeholder: "请输入店铺名称",
    },
  },
  {
    field: "businessId",
    label: "商户",
    component: "SearchSelect",

    componentprops: {
      placeholder: "请选择商户",
      showSearch: true,
      filterOption: false,
      fetchOptions: fetchBusiness,
      dataFormat: (data: any) => {
        return data.list.map((item: any) => ({
          value: item.id,
          label: item.name,
        }));
      },
    },

    // componentprops: {
    //   placeholder: "请选择商户",
    //   showSearch: true,
    //   filterOption: false,

    //   notFoundContent: null,
    // },
  },
  {
    field: "thumbnailImg",
    label: "店铺头像",
    component: "Upload",
    componentprops: {
      listType: "picture-card",
      maxCount: 1,
    },
  },
  {
    field: "carouselImages",
    label: "轮播图",
    component: "Upload",
    componentprops: {
      listType: "picture-card",
      maxCount: 5,
    },
  },
  {
    field: "description",
    label: "店铺描述",
    component: "Input",
    componentprops: {
      placeholder: "请输入店铺描述",
    },
  },
  {
    field: "address",
    label: "店铺地址",
    component: "Input",
    componentprops: {
      placeholder: "请输入店铺地址",
    },
  },
  {
    field: "phone",
    label: "店铺电话",
    component: "Input",
    componentprops: {
      placeholder: "请输入店铺电话",
    },
  },
  {
    field: "businessHours",
    label: "营业时间",
    component: "Input",
    componentprops: {
      placeholder: "请输入营业时间",
    },
  },
  {
    field: "status",
    label: "状态",
    component: "Select",
    componentprops: {
      options: statusOptions,
    },
  },
];
