import React, { useState, useEffect } from "react";
import { Drawer, message } from "antd";
import { addScheams } from "./constant";
import { BasicForm, useForm } from "@/components/form";

interface ShopModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: any) => void;
  detail: any;
}

const ShopModal: React.FC<ShopModalProps> = ({
  visible,
  onCancel,
  onOk,
  detail,
}) => {
  const [register, { setFieldsValue }] = useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (detail) {
      setFieldsValue(detail);
    }
  }, [detail]);

  const handleSave = async (values: any) => {
    try {
      setLoading(true);
      onOk(values);
    } catch (error) {
      console.error("提交表单错误:", error);
      message.error("操作失败");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Drawer
      title={detail ? "编辑店铺" : "新增店铺"}
      onClose={onCancel}
      open={visible}
      width={600}
    >
      <BasicForm
        onRegister={register}
        schemas={addScheams}
        baseColProps={{ span: 24 }}
        labelWidth={100}
        formProps={{
          onFinish: handleSave,
        }}
        formActionProps={{
          actionColOpt: { span: 24 },
          colStyle: { textAlign: "right" },
          resetButtonOptions: {
            text: "取消",
            onClick: () => {
              onCancel();
            },
          },
          submitButtonOptions: {
            text: "保存",
            type: "primary",
            htmlType: "submit",
            loading: loading,
          },
        }}
      />
    </Drawer>
  );
};

export default ShopModal;
