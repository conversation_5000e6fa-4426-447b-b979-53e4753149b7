"use client";

import React, { useState, useEffect } from "react";
import { Card, Typography, Button, Space, Descriptions, Image } from "antd";
import { useRouter } from "next/navigation";
import { useSearchParams } from "next/navigation";
import { defHttp } from "@/lib/axios";
import { useUserStore } from "@/store/useUserStore";
import { Rate } from "antd";

const { Title } = Typography;

const ShopDetailPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const shopId = searchParams.get("id");
  const [shop, setShop] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const { userInfo } = useUserStore();

  const fetchShop = async () => {
    try {
      setLoading(true);
      const data = await defHttp.get({ url: `/shop/${shopId}` });

      setShop(data);
    } catch (error) {
      console.error("获取店铺详情错误:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (shopId) {
      fetchShop();
    }
  }, [shopId]);

  return (
    <Card
      title="店铺详情"
      extra={<Button onClick={() => router.back()}>返回</Button>}
    >
      <Descriptions column={1} bordered>
        <Descriptions.Item label="店铺名称">{shop?.name}</Descriptions.Item>
        <Descriptions.Item label="店铺描述">
          {shop?.description}
        </Descriptions.Item>
        <Descriptions.Item label="店铺图片">
          {shop?.carouselImages.map((item: any) => (
            <Image width={200} src={item.imageUrl} key={item.imageUrl} />
          ))}
        </Descriptions.Item>
        <Descriptions.Item label="店铺地址">{shop?.address}</Descriptions.Item>
        <Descriptions.Item label="店铺电话">{shop?.iphone}</Descriptions.Item>
        <Descriptions.Item label="店铺评分">
          <Rate disabled defaultValue={shop?.avgRating} />
        </Descriptions.Item>
        <Descriptions.Item label="店铺状态">{shop?.status}</Descriptions.Item>
        <Descriptions.Item label="店铺营业时间">
          {shop?.businessHours}
        </Descriptions.Item>
        <Descriptions.Item label="创建时间">
          {shop?.createdAt}
        </Descriptions.Item>
        <Descriptions.Item label="更新时间">
          {shop?.updatedAt}
        </Descriptions.Item>
        <Descriptions.Item label="工位数量">
          <Space>
            {shop?.stations.length}

            <Button
              type="primary"
              onClick={() => router.push(`/main/shop/station?shopId=${shopId}`)}
            >
              查看工位
            </Button>
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label="评论数量">
          <Space>
            {shop?.reviews.length}

            <Button
              type="primary"
              onClick={() => router.push(`/main/shop/review?shopId=${shopId}`)}
            >
              查看评论
            </Button>
          </Space>
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );
};

export default ShopDetailPage;
