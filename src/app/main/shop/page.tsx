"use client";

import React, { useState, useRef } from "react";
import { Card, Space, message } from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import { defHttp } from "@/lib/axios";
import { useRouter } from "next/navigation";
import { BasicForm, FormProps, useForm } from "@/components/form";
import { columns, searchScheams } from "./constant";
import ListHeader from "@/components/listHeader";
import { BasicTable } from "@/components/table";
import ShopModal from "./Modal";

export default function UsersPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  // 删除店铺
  const handleDelete = async (id: number) => {
    try {
      setLoading(true);
      await defHttp.delete({ url: `/shop/${id}` });

      message.success("删除店铺成功");
      tableRef.current?.reload();
    } catch (error) {
      console.error("删除店铺错误:", error);
      message.error("删除店铺失败");
    } finally {
      setLoading(false);
    }
  };
  const [modalVisible, setModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<any>(null);
  const handleEdit = (record: any) => {
    setEditingUser(record);
    setModalVisible(true);
  };

  const handleSave = (values: any) => {
    console.log("保存", values);
    values.thumbnailImg = values.thumbnailImg[0]?.url;
    values.carouselImages = values.carouselImages.map(
      (item: any, index: number) => ({
        imageUrl: item.url,
        order: index,
      })
    );
    if (editingUser) {
      // 编辑
      defHttp.put({
        url: `/shop/${editingUser.id}`,
        data: values,
      });
    } else {
      // 新增
      defHttp.post({ url: "/shop", data: values });
    }
    message.success("保存成功");
    setModalVisible(false);
    setEditingUser(null);
    tableRef.current?.reload();
  };

  const fetchshop = (params: any) => {
    return defHttp.get({ url: `/shop`, params });
  };

  const tableRef = useRef<any>(null);

  const [searchInfo, setSearchInfo] = useState<any>({});

  const formProps: FormProps = {
    showAdvancedButton: true,
    actionSpan: 3,
    labelWidth: 100,
    baseColProps: { lg: 8, md: 12, sm: 24 },
    formActionProps: {
      actionColOpt: { span: 24 },
      colStyle: { textAlign: "right" },
      resetButtonOptions: {
        text: "重置",
      },
      submitButtonOptions: {
        text: "确定",
        htmlType: "submit",
        loading: loading,
      },
      advancedButtonOptions: { style: { fontSize: 12 } },
    },
  };
  const handleSearch = (values: any) => {
    setSearchInfo(values);
  };

  return (
    <>
      <Card>
        <Space direction="vertical" size="middle">
          {/* 页面标题和操作按钮 */}
          <ListHeader
            title="店铺管理"
            description="管理系统店铺，包括店铺信息、角色权限等"
            onRefresh={() => tableRef.current?.reload()}
            onExport={() => message.info("导出功能开发中")}
            onCreate={() => {
              handleEdit(null);
            }}
          />

          {/* 搜索和筛选 */}

          <BasicForm
            {...formProps}
            schemas={searchScheams}
            formProps={{
              onFinish: handleSearch,
            }}
          />

          {/* 店铺表格 */}
          <BasicTable
            ref={tableRef}
            api={fetchshop}
            columns={columns}
            rowKey="id"
            searchInfo={searchInfo}
            tableProps={{
              pagination: {
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              },
            }}
            actionProps={{ title: "操作", width: 200, fixed: "right" }}
            actions={[
              {
                label: "",
                icon: EditOutlined,
                onClick: (record) => handleEdit(record),
              },
              {
                label: "",
                icon: DeleteOutlined,
                color: "error",
                popConfirm: { title: "是否删除？", confirm: handleDelete },
              },
              {
                label: "",
                icon: EyeOutlined,
                onClick: (record) => {
                  router.push(`/main/shop/detail?id=${record.id}`);
                },
              },
            ]}
          />
        </Space>
      </Card>
      <ShopModal
        visible={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingUser(null);
        }}
        onOk={handleSave}
        detail={editingUser}
      />
    </>
  );
}
