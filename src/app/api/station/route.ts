import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/db";
import { z } from "zod";
import { validateRequest } from "@/lib/validate";
import { cleanObject } from "@/utils/cleanObject";

const createStationSchema = z.object({
  name: z.string().min(1, "工位名不能为空"),
  shopId: z.coerce.number().min(1, "店铺ID不能为空"),
  price: z.coerce.number().min(0, "价格不能小于0").optional(),
  status: z.enum(["IDLE", "IN_USE", "MAINTENANCE"]).optional(),
  description: z.string().optional(),
  thumbnailImg: z.string().optional(),
});

export type CreateStationBody = z.infer<typeof createStationSchema>;



// GET - 获取用户列表
export async function GET(request: NextRequest) {
  try {
    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    const keyword = searchParams.get("keyword") || "";
    const status = searchParams.get("status") || "";

    // 过滤数据 分页
    // let filteredUsers = mockUsers;
    const filteredUsers = await prisma.station.findMany({
      where: {
        name: {
          contains: keyword,
        },
        status: status ? (status as any) : undefined,
      },
      skip: (page - 1) * pageSize,
      take: pageSize,
    });

    // 分页
    const total = filteredUsers.length;

    return NextResponse.json({
      success: true,
      code: 200,
      message: "获取用户列表成功",
      data: {
        list: filteredUsers,
        pagination: {
          current: page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize),
        },
      },
    });
  } catch (error) {
    console.error("获取用户列表错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// POST - 创建新用户
export async function POST(request: NextRequest) {
  try {

    const validationResult = await validateRequest(request, createStationSchema);


    const { name, status = "IDLE", description, shopId, price = 0, thumbnailImg } = validationResult;


    // 检查用户名是否已存在
    const existingUser = await prisma.station.findFirst({
      where: {
        name: name,
      },
    });
    if (existingUser) {
      return NextResponse.json(
        { success: false, code: 400, message: "商户名已存在", data: null },
        { status: 400 }
      );
    }

    // 创建新用户
    const newUser = {
      name,
      status,
      description,
      shopId,
      price,
      thumbnailImg,
    };
    await prisma.station.create({
      data: newUser,
    });

    // mockUsers.push(newUser);

    return NextResponse.json({
      success: true,
      code: 200,
      message: "创建用户成功",
      data: newUser,
    });
  } catch (error) {
    console.error("创建用户错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// OPTIONS - 处理CORS预检请求
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}
