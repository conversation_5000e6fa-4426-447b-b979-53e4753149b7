import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/db";
import { getGeo } from "@/utils/getGeo";
import { validateRequest } from "@/lib/validate";
import { cleanObject } from "@/utils/cleanObject";
import { z } from "zod";

// GET - 获取单个店铺信息
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const shopId = parseInt(id);
    // const targetShop = mockShops.find((u) => u.id === shopId);
    const targetShop = await prisma.shop.findUnique({
      where: {
        id: shopId,
      },
      include: {
        stations: true,
        carouselImages: {
          select: {
            imageUrl: true,
          },
          where: {
            pageId: 2,
          },
        },
        reviews: true,
      },
    });

    //  stations和订单关联
    if (targetShop) {
      if (targetShop.status === "ACTIVE") {
        const stationOrderStatus = await Promise.all(
          targetShop.stations.map(async (station) => {
            const latestOrder = await prisma.order.findFirst({
              include: {
                consumeOrder: {
                  where: {
                    stationId: station.id,
                  },
                },
              },
              orderBy: {
                createdAt: "desc",
              },
            });
            return latestOrder?.status;
          })
        );
        targetShop.stations = targetShop.stations.map((station, index) => ({
          ...station,
          orderStatus: stationOrderStatus[index],
        }));
      }
    }

    if (!targetShop) {
      return NextResponse.json(
        { success: false, code: 404, message: "店铺不存在", data: null },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      code: 200,
      message: "获取店铺信息成功",
      data: targetShop,
    });
  } catch (error) {
    console.error("获取店铺信息错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

const updateShopSchema = z.object({
  name: z.string().min(1, "店铺名不能为空"),
  description: z.string().optional(),
  address: z.string().min(1, "地址不能为空").optional(),
  businessHours: z.string().optional(),
  phone: z.string().min(1, "手机号不能为空").optional(),
  thumbnailImg: z.string().optional(),
  status: z.string().optional(),
  carouselImages: z.array(z.string()).optional(),
});
// PUT - 更新店铺信息
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const shopId = parseInt(id);
    // const shopIndex = mockShops.findIndex((u) => u.id === shopId);
    const shopIndex = await prisma.shop.findUnique({
      where: {
        id: shopId,
      },
    });

    if (!shopIndex) {
      return NextResponse.json(
        { success: false, code: 404, message: "店铺不存在", data: null },
        { status: 404 }
      );
    }

    const body = await validateRequest(request, updateShopSchema);


    const { address, carouselImages } = body;



    let updatedShop: any = cleanObject(body);

    if (address) {
      const { longitude, latitude } = await getGeo(address);

      if (longitude && latitude) {
        updatedShop = {
          ...updatedShop,
          longitude: Number(longitude),
          latitude: Number(latitude),
        };
      }
    }

    await prisma.shop.update({
      where: {
        id: shopId,
      },
      data: {
        ...updatedShop,
        updatedAt: new Date(),
      },
    });
    if (carouselImages) {
      await prisma.carousel_images.deleteMany({
        where: {
          shopId,
        },
      });
      await prisma.carousel_images.createMany({
        data: carouselImages.map((item: any) => ({
          imageUrl: item.imageUrl,
          order: item.order,
          shopId,
          pageId: 2,
        })),
      });
    }
    return NextResponse.json({
      success: true,
      code: 200,
      message: "更新店铺信息成功",
      data: updatedShop,
    });
  } catch (error) {
    console.error("更新店铺信息错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// DELETE - 删除店铺
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const shopId = parseInt(id);

    // 不允许删除管理员账户
    if (shopId === 1) {
      return NextResponse.json(
        {
          success: false,
          code: 400,
          message: "不能删除管理员账户",
          data: null,
        },
        { status: 400 }
      );
    }

    const shopIndex = await prisma.shop.findUnique({
      where: {
        id: shopId,
      },
    });
    if (!shopIndex) {
      return NextResponse.json(
        { success: false, code: 404, message: "店铺不存在", data: null },
        { status: 404 }
      );
    }

    await prisma.shop.delete({
      where: {
        id: shopId,
      },
    });
    return NextResponse.json({
      success: true,
      code: 200,
      message: "删除店铺成功",
      data: shopIndex,
    });
  } catch (error) {
    console.error("删除店铺错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// OPTIONS - 处理CORS预检请求
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}
