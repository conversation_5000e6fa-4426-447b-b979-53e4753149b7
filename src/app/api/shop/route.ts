import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/db";
import { z } from "zod";
import { getGeo } from "@/utils/getGeo";

// GET - 获取店铺列表
export async function GET(request: NextRequest) {
  try {
    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    const keyword = searchParams.get("keyword") || "";
    const status = searchParams.get("status") || "";

    // 查询所有洗车店 和 工位
    const shops = await prisma.shop.findMany({
      include: {
        stations: true,
        reviews: true,
      },
      where: {
        name: {
          contains: keyword,
        },
        status: status ? (status as any) : undefined,
      },
      skip: (page - 1) * pageSize,
      take: pageSize,
    });

    // 分页
    const total = shops.length;

    return NextResponse.json({
      success: true,
      code: 200,
      message: "获取店铺列表成功",
      data: {
        list: shops,
        pagination: {
          current: page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize),
        },
      },
    });
  } catch (error) {
    console.error("获取店铺列表错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// POST - 创建新店铺
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    // 定义验证schema
    const createBusinessSchema = z.object({
      name: z.string().min(1, "店铺名不能为空"),
      iphone: z.string().min(1, "手机号不能为空"),
      address: z.string().min(1, "地址不能为空"),
    });

    // 验证请求数据
    const validationResult = createBusinessSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          code: 400,
          message: validationResult.error.issues[0].message,
          data: null,
        },
        { status: 400 }
      );
    }

    const { name, iphone, address } = validationResult.data;
    const {
      status = "ACTIVE",
      businessId,
      description,

      businessHours,
      thumbnailImg,
      carouselImages,
    } = body;

    const { longitude, latitude } = await getGeo(address);

    // 检查店铺名是否已存在
    const existingUser = await prisma.shop.findFirst({
      where: {
        name: name,
      },
    });
    if (existingUser) {
      return NextResponse.json(
        { success: false, code: 400, message: "店铺名已存在", data: null },
        { status: 200 }
      );
    }

    // 创建新店铺
    const newUser = {
      name,
      iphone,
      status,
      businessId,
      description,
      address,
      businessHours,
      thumbnailImg,
      longitude: Number(longitude),
      latitude: Number(latitude),
    };
    const createdUser = await prisma.shop.create({
      data: newUser,
    });

    if (carouselImages) {

      await prisma.carousel_images.createMany({
        data: carouselImages.map((item: any) => {


          return {
            imageUrl: item.imageUrl,
            shopId: createdUser.id,
            pageId: 2,
            order: item.order,
          };
        }),
      });
    }

    // mockUsers.push(newUser);

    return NextResponse.json({
      success: true,
      code: 200,
      message: "创建店铺成功",
      data: newUser,
    });
  } catch (error) {
    console.error("创建店铺错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// OPTIONS - 处理CORS预检请求
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}
